import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FiMapPin, FiHome, FiCalendar, FiTrendingUp } from 'react-icons/fi';
import '../styles/ProjectCard.css';

const ProjectCard = ({ project, index, inView, onClick, showViewDetails = false }) => {
  const navigate = useNavigate();
  const isOngoing = project.status === 'ongoing';
  const isUpcoming = project.status === 'upcoming';
  const isCompleted = project.status === 'completed';

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut",
        delay: index * 0.1
      }
    }
  };

  const formatPrice = (price) => {
    if (!price) return 'Price on Request';
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)}L`;
    }
    return `₹${price.toLocaleString()}`;
  };

  const getStatusColor = () => {
    switch (project.status) {
      case 'upcoming': return 'var(--status-upcoming)';
      case 'ongoing': return 'var(--status-ongoing)';
      case 'completed': return 'var(--status-completed)';
      default: return 'var(--text-secondary)';
    }
  };

  const getExpectedCompletion = () => {
    if (project.timeline?.expectedCompletion) {
      return new Date(project.timeline.expectedCompletion).getFullYear();
    }
    if (project.timeline?.actualCompletion) {
      return new Date(project.timeline.actualCompletion).getFullYear();
    }
    if (project.year) {
      return project.year;
    }
    return 'TBD';
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/projects/${project._id || project.id || project.slug}`);
    }
  };

  const truncateText = (text, maxLength = 60) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
  };

  // Use dummy image if no project image is available
  const getProjectImage = () => {
    if (project.heroImage) return project.heroImage;
    if (project.images?.[0]?.url) return project.images[0].url;
    if (project.image) return project.image;
    // Fallback to a high-quality real estate image
    return 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80';
  };

  return (
    <motion.div
      className="project-card compact"
      variants={cardVariants}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      whileHover={{
        y: -4,
        transition: { duration: 0.2 }
      }}
      onClick={handleCardClick}
      style={{ cursor: 'pointer' }}
    >
      <div className="card-image">
        <img
          src={getProjectImage()}
          alt={project.title}
          loading="lazy"
        />

        {/* Status Badge */}
        <div className="status-badge" style={{ backgroundColor: getStatusColor() }}>
          {project.status?.charAt(0).toUpperCase() + project.status?.slice(1)}
        </div>

        {/* Featured Badge */}
        {project.isFeatured && (
          <div className="featured-badge">
            <FiTrendingUp size={12} />
            Featured
          </div>
        )}

        {/* Progress Indicator for Ongoing Projects */}
        {isOngoing && project.progress !== undefined && (
          <div className="progress-indicator">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${project.progress}%` }}
              />
            </div>
            <span className="progress-text">{project.progress}%</span>
          </div>
        )}
      </div>

      <div className="card-content">
        <div className="card-header">
          <h3 className="project-title">{project.title}</h3>
          <div className="project-location">
            <FiMapPin size={12} />
            <span>{project.location}</span>
          </div>
        </div>

        <p className="project-description">
          {truncateText(project.shortDescription || project.description)}
        </p>

        <div className="project-details">
          <div className="detail-row">
            <div className="detail-item">
              <FiHome size={14} />
              <span className="detail-value">{project.totalUnits || project.units || 'N/A'}</span>
              <span className="detail-label">Units</span>
            </div>
            <div className="detail-item">
              <FiCalendar size={14} />
              <span className="detail-value">{getExpectedCompletion()}</span>
              <span className="detail-label">{isCompleted ? 'Completed' : 'Expected'}</span>
            </div>
          </div>

          <div className="price-section">
            <span className="price-value">
              {project.value || formatPrice(project.startingPrice) || 'Price on Request'}
            </span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProjectCard;
