/* Compact Project Card Styles */
.project-card.compact {
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
  position: relative;
  box-shadow: var(--shadow-sm);
  width: 100%;
  cursor: pointer;
}

.project-card.compact:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Card Image */
.project-card .card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.project-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-card:hover .card-image img {
  transform: scale(1.03);
}

/* Status Badge */
.status-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
  z-index: 2;
}

/* Featured Badge */
.featured-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: var(--accent-gradient);
  color: white;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(10px);
  z-index: 2;
}

/* Progress Indicator for Ongoing Projects */
.progress-indicator {
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  color: white;
  font-weight: 600;
  font-size: 11px;
  min-width: 30px;
  text-align: right;
}

/* Card Content */
.project-card .card-content {
  padding: 16px;
}

.card-header {
  margin-bottom: 12px;
}

.project-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 6px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-location {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 13px;
}

.project-description {
  color: var(--text-secondary);
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Project Details */
.project-details {
  border-top: 1px solid var(--border-primary);
  padding-top: 12px;
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--text-secondary);
  font-size: 12px;
}

.detail-item svg {
  color: var(--accent-primary);
  flex-shrink: 0;
}

.detail-label {
  font-size: 10px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.detail-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 4px;
}

/* Price Section */
.price-section {
  text-align: center;
  padding: 8px 0;
}

.price-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--accent-primary);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Remove card actions since we're making the whole card clickable */

/* Status Colors */
:root {
  --status-upcoming: #3b82f6;
  --status-ongoing: #f59e0b;
  --status-completed: #10b981;
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-card .card-image {
    height: 180px;
  }

  .project-card .card-content {
    padding: 14px;
  }

  .project-title {
    font-size: 16px;
    line-height: 1.3;
    margin-bottom: 6px;
  }

  .project-description {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 12px;
  }

  .detail-row {
    gap: 10px;
    margin-bottom: 10px;
  }

  .detail-item {
    font-size: 11px;
    gap: 4px;
  }

  .detail-value {
    font-size: 12px;
  }

  .price-value {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .project-card .card-image {
    height: 160px;
  }

  .project-card .card-content {
    padding: 12px;
  }

  .project-title {
    font-size: 15px;
  }

  .project-description {
    font-size: 11px;
    margin-bottom: 10px;
  }

  .detail-item {
    font-size: 10px;
  }

  .detail-value {
    font-size: 11px;
  }

  .price-value {
    font-size: 13px;
  }

  .progress-indicator {
    bottom: 8px;
    left: 8px;
    right: 8px;
    padding: 4px 6px;
  }

  .progress-text {
    font-size: 10px;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .project-card {
    transition: none;
  }

  .project-card:hover {
    transform: none;
  }

  .project-card:hover .card-image img {
    transform: none;
  }

  .progress-indicator {
    opacity: 1; /* Always show progress on touch devices */
  }
}
