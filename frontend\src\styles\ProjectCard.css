/* Modern Project Card Styles */
.project-card.modern {
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
  position: relative;
  box-shadow: var(--shadow-sm);
  width: 100%;
}

.project-card.modern:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
  
}

/* Card Image */
.project-card .card-image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.project-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.project-card:hover .card-image img {
  transform: scale(1.05);
}

/* Status Badge */
.status-badge {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}
.status-badgefortable {
  
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

/* Featured Badge */
.featured-badge {
  position: absolute;

  background: var(--accent-gradient);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-1);
  backdrop-filter: blur(10px);
}

/* Progress Circle Overlay */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

.project-card:hover .card-overlay {
  opacity: 1;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-text {
  position: absolute;
  color: white;
  font-weight: 700;
  font-size: var(--text-sm);
}

/* Card Content */
.project-card .card-content {
  padding: var(--space-6);
}

.card-header {
  margin-bottom: var(--space-4);
}

.project-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1.3;
}

.project-location {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.project-description {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.6;
  
}

/* Project Details */
.project-details {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-4);
}

.detail-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--text-secondary);
}

.detail-item svg {
  color: var(--accent-primary);
  flex-shrink: 0;
}

.detail-item div {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.detail-value {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
}

/* Price Section */
.price-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-3) 0;
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.price-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.price-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--accent-primary);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card Actions */
.card-actions {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.view-details-btn {
  display: block;
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--accent-gradient);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  text-align: center;
  transition: var(--transition-normal);
}

.view-details-btn:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Status Colors */
:root {
  --status-upcoming: #3b82f6;
  --status-ongoing: #f59e0b;
  --status-completed: #10b981;
}

/* Responsive Design */
@media (max-width: 768px) {
  .project-card .card-image {
    height: 220px;
  }

  .project-card .card-content {
    padding: var(--space-5);
  }

  .project-title {
    font-size: var(--text-xl);
    line-height: 1.3;
    margin-bottom: var(--space-3);
  }

  .project-description {
    font-size: var(--text-sm);
    line-height: 1.5;
    margin-bottom: var(--space-4);
  }

  .detail-row {
    
    gap: var(--space-3);
  }

  .detail-item {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    background: var(--glass-bg);
    border: 1px solid var(--border-primary);
  }

  .detail-label {
    font-size: var(--text-xs);
    margin-bottom: var(--space-1);
  }

  .detail-value {
    font-size: var(--text-sm);
    font-weight: 600;
  }

  .view-details-btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    margin-top: var(--space-4);
  }
}

@media (max-width: 480px) {
  .project-card .card-image {
    height: 200px;
  }

  .project-card .card-content {
    padding: var(--space-4);
  }

  .project-title {
    font-size: var(--text-lg);
  }

  .project-description {
    font-size: var(--text-xs);
    margin-bottom: var(--space-3);
  }

  .progress-circle {
    transform: scale(0.8);
  }

  .detail-item {
    padding: var(--space-2);
  }

  .detail-label {
    font-size: var(--text-xs);
  }

  .detail-value {
    font-size: var(--text-xs);
  }

  .view-details-btn {
    padding: var(--space-3);
    font-size: var(--text-xs);
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .project-card {
    transition: none;
  }

  .project-card:hover {
    transform: none;
  }

  .project-card:hover .card-image img {
    transform: none;
  }

  .card-overlay {
    opacity: 1; /* Always show progress on touch devices */
  }

  .view-details-btn {
    min-height: 44px; /* Minimum touch target size */
  }
}
