/* Enhanced Mobile Responsive Design for Card Components */

/* Base Mobile Improvements */
@media (max-width: 768px) {
  /* Container adjustments */
  .container {
    padding: 0 var(--space-4);
  }

  /* Project Cards - Home Page */
  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
    padding: 0 var(--space-3);
  }

  /* Project Card Improvements */
  .project-card {
    max-width: 100%;
    margin: 0 auto;
  }

  .project-card .card-image {
    height: 220px;
  }

  .project-card .card-content {
    padding: var(--space-5);
  }

  .project-title {
    font-size: var(--text-xl);
    line-height: 1.3;
    margin-bottom: var(--space-3);
  }

  .project-description {
    font-size: var(--text-sm);
    line-height: 1.5;
    margin-bottom: var(--space-4);
  }

  /* Detail rows in cards */
  .detail-row {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .detail-item {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    background: var(--glass-bg);
  }

  /* Team Member Cards - About Page */
  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .team-card {
    max-width: 400px;
    margin: 0 auto;
  }

  .member-image {
    height: 250px;
  }

  .member-info {
    padding: var(--space-5);
  }

  .member-name {
    font-size: var(--text-xl);
    margin-bottom: var(--space-2);
  }

  .member-position {
    font-size: var(--text-base);
    margin-bottom: var(--space-1);
  }

  .member-experience {
    font-size: var(--text-sm);
    margin-bottom: var(--space-3);
  }

  .member-bio {
    font-size: var(--text-sm);
    line-height: 1.6;
  }

  /* Hero Stats */
  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-3);
    margin: var(--space-8) 0;
  }

  .stat {
    padding: var(--space-3);
    min-width: auto;
  }

  .stat-number {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-1);
  }

  .stat-label {
    font-size: var(--text-xs);
    line-height: 1.3;
  }

  /* Values Grid */
  .values-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .value-card {
    padding: var(--space-6);
    text-align: center;
  }

  .value-icon {
    margin-bottom: var(--space-4);
  }

  /* Timeline adjustments */
  .timeline::before {
    left: var(--space-4);
  }

  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin-left: var(--space-12);
    margin-right: 0;
    padding: var(--space-5);
    text-align: left;
  }

  .timeline-year {
    font-size: var(--text-lg);
    margin-bottom: var(--space-2);
  }

  .timeline-event {
    font-size: var(--text-lg);
    margin-bottom: var(--space-2);
  }

  .timeline-description {
    font-size: var(--text-sm);
    line-height: 1.5;
  }

  /* Button improvements */
  .view-all-btn {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-sm);
    width: auto;
    min-width: 200px;
  }

  .view-details-btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }

  /* CTA Section */
  .cta-content {
    padding: var(--space-8) var(--space-4);
    text-align: center;
  }

  .cta-content h2 {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-4);
  }

  .cta-content p {
    font-size: var(--text-base);
    margin-bottom: var(--space-6);
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
    padding: var(--space-4) var(--space-6);
  }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
  /* Container */
  .container {
    padding: 0 var(--space-3);
  }

  /* Project Cards */
  .project-card .card-image {
    height: 200px;
  }

  .project-card .card-content {
    padding: var(--space-4);
  }

  .project-title {
    font-size: var(--text-lg);
  }

  .project-description {
    font-size: var(--text-xs);
    margin-bottom: var(--space-3);
  }

  /* Team Cards */
  .team-card {
    max-width: 100%;
  }

  .member-image {
    height: 220px;
  }

  .member-info {
    padding: var(--space-4);
  }

  .member-name {
    font-size: var(--text-lg);
  }

  .member-bio {
    font-size: var(--text-xs);
  }

  /* Hero Stats - Stack vertically on very small screens */
  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--space-4);
    max-width: 250px;
    margin: var(--space-6) auto;
  }

  .stat {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    background: var(--glass-bg);
    border: 1px solid var(--border-primary);
  }

  .stat-number {
    font-size: var(--text-3xl);
    margin-bottom: var(--space-2);
  }

  .stat-label {
    font-size: var(--text-sm);
  }

  /* Values */
  .value-card {
    padding: var(--space-5);
  }

  .value-title {
    font-size: var(--text-lg);
  }

  .value-description {
    font-size: var(--text-sm);
  }

  /* Timeline */
  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin-left: var(--space-10);
    padding: var(--space-4);
  }

  .timeline-year {
    font-size: var(--text-base);
  }

  .timeline-event {
    font-size: var(--text-base);
  }

  .timeline-description {
    font-size: var(--text-xs);
  }

  /* Buttons */
  .view-all-btn {
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-xs);
    min-width: 180px;
  }

  .view-details-btn {
    padding: var(--space-3);
    font-size: var(--text-xs);
  }

  /* Progress circle scaling */
  .progress-circle {
    transform: scale(0.8);
  }

  /* Section spacing */
  .projects-section {
    padding: var(--space-12) 0;
  }

  .section-header {
    margin-bottom: var(--space-8);
  }

  .section-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }

  .section-subtitle {
    font-size: var(--text-sm);
  }
}

/* Extra Small Devices */
@media (max-width: 360px) {
  .container {
    padding: 0 var(--space-2);
  }

  .project-card .card-content {
    padding: var(--space-3);
  }

  .member-info {
    padding: var(--space-3);
  }

  .hero-stats {
    max-width: 200px;
  }

  .stat {
    padding: var(--space-3);
  }

  .stat-number {
    font-size: var(--text-2xl);
  }

  .value-card {
    padding: var(--space-4);
  }

  .timeline-item:nth-child(odd) .timeline-content,
  .timeline-item:nth-child(even) .timeline-content {
    margin-left: var(--space-8);
    padding: var(--space-3);
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .project-card,
  .team-card,
  .value-card {
    transition: none;
  }

  .project-card:hover,
  .team-card:hover,
  .value-card:hover {
    transform: none;
  }

  .view-all-btn,
  .view-details-btn,
  .cta-button {
    min-height: 44px; /* Minimum touch target size */
    padding: var(--space-3) var(--space-6);
  }

  /* Remove hover effects on touch devices */
  .project-card:hover .card-image img,
  .team-card:hover .member-image img {
    transform: none;
  }

  .card-overlay {
    opacity: 1; /* Always show progress on touch devices */
  }
}
